#encoding:gbk

# 全局参数配置
open_long_num = 2
open_short_num = 2
hand = 1  # 基础交易张数（将根据波动率动态调整）
moving_tick = 0.020  # 移动止损点位
fs_line = 0.010  # FS即时价位—分时均线价位<0.1
sleep_time = 5  # 分钟 1分钟内不连续开同方向仓位
stoploss_ratio = -20  # % 止损比例
stopprofit_ratio = 20  # % 盈利比例
expiry_days_threshold = 15  # 优化：延长到15天，避免时间价值快速衰减
hold_days_limit = 5  # 持仓最大天数，可调参数

enable_ma55_filter = False  # 是否启用MA55过滤条件 or True False

# 新增优化参数
enable_multi_signal_confirmation = True  # 启用多重信号确认
enable_volatility_filter = True  # 启用波动率过滤
enable_volume_confirmation = True  # 启用成交量确认
enable_liquidity_check = True  # 启用流动性检查
enable_dynamic_position_sizing = True  # 启用动态仓位管理

# 多重信号确认模式选择
# "strict": K2信号 + 至少1个辅助确认（推荐用于震荡市）
# "balanced": K2信号 + 辅助确认或高波动率环境（推荐用于一般市场）
# "loose": 仅K2信号（推荐用于趋势市或高频交易）
signal_confirmation_mode = "balanced"

# ==================== 全新专业期权交易策略参数 ====================

# 1. 波动率驱动策略参数
vix_threshold_low = 15      # VIX低位阈值，<15时市场过于平静
vix_threshold_high = 30     # VIX高位阈值，>30时市场过于恐慌
iv_percentile_buy_min = 20  # IV分位数最低20%才考虑买入期权
iv_percentile_buy_max = 60  # IV分位数超过60%避免买入期权
iv_hv_ratio_optimal = 1.2   # IV/HV最优比值，略高于历史波动率
realized_vol_min = 0.12     # 实现波动率最低12%
realized_vol_max = 0.35     # 实现波动率最高35%

# 2. 技术信号强化参数
trend_strength_threshold = 0.75  # 趋势强度阈值
momentum_lookback = 10           # 动量计算回望期
breakout_threshold = 1.5         # 突破强度阈值（标准差倍数）
volume_surge_ratio = 2.0         # 成交量激增比例
price_acceleration_min = 0.015   # 价格加速度最小值1.5%

# 3. 市场微观结构参数
bid_ask_spread_max = 0.05       # 最大买卖价差5%
min_open_interest = 100         # 最小持仓量
min_daily_volume = 50           # 最小日成交量
liquidity_score_min = 0.7       # 流动性评分最低0.7

# 4. 风险控制参数
max_daily_trades = 2            # 每日最大2次交易（严格控制）
max_weekly_trades = 8           # 每周最大8次交易
cooldown_after_loss = 7200      # 亏损后2小时冷静期
max_consecutive_losses = 3      # 最大连续亏损次数
daily_risk_budget = 0.02        # 每日风险预算2%
position_concentration_max = 0.3 # 单一标的最大仓位30%

# 5. 策略选择参数
strategy_mode = "adaptive"      # 策略模式：adaptive/conservative/aggressive
enable_spread_strategies = True  # 启用价差策略
enable_straddle_strategies = True # 启用跨式策略
enable_calendar_strategies = True # 启用日历策略

# 6. 合约选择优化参数
delta_target_range = [0.3, 0.7]  # 目标Delta范围
days_to_expiry_min = 14          # 最小到期天数
days_to_expiry_max = 45          # 最大到期天数
gamma_risk_threshold = 0.1       # Gamma风险阈值
theta_decay_acceptable = -0.05   # 可接受的Theta衰减

# 波动率相关参数
min_volatility_threshold = 0.12  # 最低波动率阈值
high_volatility_threshold = 0.30  # 高波动率阈值
volatility_lookback_days = 20  # 波动率计算回望天数

# 流动性相关参数
min_daily_volume = 50  # 最小日成交量
max_bid_ask_spread = 0.08  # 最大买卖价差（8%）
min_open_interest = 500  # 最小未平仓合约数

# 动态仓位参数
max_single_risk_ratio = 0.02  # 单次交易最大风险比例（账户的2%）
max_position_size = 5  # 最大单次交易张数

# 账户配置 - 已设置为实际账户
account = "***********"  # 实际账户ID

# 重要提示：以下函数在生产环境中需要替换为实际的交易平台函数
# 1. passorder - 下单函数
# 2. get_stock_list_in_sector - 获取板块股票列表
# 3. download_history_data - 下载历史数据（可选）

import math
import time
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

class G():
    pass

g = G()
g.buy_long = 0
g.buy_short = 0
g.hold = 0
g.hold_price = 0
g.open_price = 0
g.trace_time_long = 0
g.trace_time_short = 0
g.opened_t = []
g.hold_code = ''
# 新增优化相关的全局变量
g.account_balance = 1000000  # 假设账户余额100万，实际使用时需要动态获取
g.prev_k2_5m = 0
g.prev_k2_30m = 0

# 模拟的获取板块股票列表函数
def get_stock_list_in_sector(sector_name):
    """
    模拟的获取板块股票列表函数
    在实际环境中，这个函数应该由交易平台提供
    """
    try:
        # 在实际环境中，这里应该调用平台提供的函数
        # 现在返回空列表，避免运行时错误
        print(f"警告: get_stock_list_in_sector函数为模拟版本，板块: {sector_name}")
        return []
    except Exception as e:
        print(f"获取板块股票列表时出错: {e}")
        return []

# 安全的下单函数包装器
def safe_passorder(order_type, market, acct, code, price_type, price, volume, stop_price, order_flag, note, context):
    """
    安全的下单函数包装器，包含错误处理
    """
    try:
        print(f"准备下单: {note}")
        print(f"订单参数: 类型={order_type}, 市场={market}, 账户={acct}, 合约={code}")
        print(f"价格类型={price_type}, 价格={price}, 数量={volume}")

        # 检查必要的参数
        if not code:
            print("错误: 合约代码为空")
            return None

        if not acct:
            print("错误: 账户为空")
            return None

        # 调用实际的下单函数
        try:
            # 尝试实际下单 - 生产环境
            try:
                order_id = passorder(order_type, market, acct, code, price_type, price, volume, stop_price, order_flag, note, context)
                print(f"实际下单成功: {note}, 订单ID: {order_id}")
                return order_id
            except NameError:
                # 如果passorder函数不存在，使用模拟模式
                print("警告: passorder函数未定义，使用模拟模式")
                print(f"模拟下单: {note}")
                order_id = f"MOCK_ORDER_{int(time.time())}"
                return order_id
            except Exception as e:
                print(f"实际下单失败: {e}")
                # 下单失败时不使用模拟，直接返回None
                return None

        except Exception as e:
            print(f"下单过程异常: {e}")
            return None

    except Exception as e:
        print(f"下单包装器异常: {e}")
        return None

# ==================== 优化功能函数 ====================

def calculate_realized_volatility(price_data, days=20):
    """
    计算实现波动率
    """
    try:
        if len(price_data) < days + 1:
            return 0.20  # 默认波动率

        # 计算日收益率
        returns = price_data['close'].pct_change().dropna()

        # 计算年化波动率
        volatility = returns.rolling(window=days).std() * np.sqrt(252)
        return volatility.iloc[-1] if not pd.isna(volatility.iloc[-1]) else 0.20
    except Exception as e:
        print(f"计算波动率时出错: {e}")
        return 0.20

def volume_confirmation(price_data, multiplier=1.5):
    """
    成交量确认：当前成交量是否超过20日均量的1.5倍
    """
    try:
        if len(price_data) < 21:
            return True  # 数据不足时默认通过

        volume_ma = price_data['volume'].rolling(20).mean()
        current_volume = price_data['volume'].iloc[-1]
        avg_volume = volume_ma.iloc[-1]

        result = current_volume > avg_volume * multiplier
        print(f"成交量确认: 当前量={current_volume:.0f}, 20日均量={avg_volume:.0f}, 倍数={current_volume/avg_volume:.2f}, 结果={result}")
        return result
    except Exception as e:
        print(f"成交量确认时出错: {e}")
        return True

def check_price_breakout(price_data, lookback=10):
    """
    价格突破确认：检查是否突破近期高低点
    """
    try:
        if len(price_data) < lookback + 1:
            return False

        current_close = price_data['close'].iloc[-1]
        recent_high = price_data['high'].iloc[-lookback:-1].max()
        recent_low = price_data['low'].iloc[-lookback:-1].min()

        # 突破近期高点或低点
        breakout_up = current_close > recent_high
        breakout_down = current_close < recent_low

        result = breakout_up or breakout_down
        print(f"价格突破确认: 当前价={current_close:.2f}, {lookback}日高点={recent_high:.2f}, 低点={recent_low:.2f}, 突破={result}")
        return result
    except Exception as e:
        print(f"价格突破确认时出错: {e}")
        return False

def calculate_rsi(price_data, period=14):
    """
    计算RSI指标
    """
    try:
        if len(price_data) < period + 1:
            return 50  # 默认中性值

        delta = price_data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()

        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))

        return rsi.iloc[-1] if not pd.isna(rsi.iloc[-1]) else 50
    except Exception as e:
        print(f"计算RSI时出错: {e}")
        return 50

def check_rsi_divergence(price_data):
    """
    检查RSI背离
    """
    try:
        if len(price_data) < 30:
            return False

        rsi_values = []
        for i in range(len(price_data) - 20, len(price_data)):
            if i >= 14:
                rsi = calculate_rsi(price_data.iloc[:i+1])
                rsi_values.append(rsi)

        if len(rsi_values) < 10:
            return False

        # 简单的背离检测：价格新高但RSI未新高，或价格新低但RSI未新低
        recent_prices = price_data['close'].iloc[-10:].values
        recent_rsi = rsi_values[-10:]

        price_trend = recent_prices[-1] - recent_prices[0]
        rsi_trend = recent_rsi[-1] - recent_rsi[0]

        # 背离：价格和RSI趋势相反
        divergence = (price_trend > 0 and rsi_trend < 0) or (price_trend < 0 and rsi_trend > 0)

        print(f"RSI背离检查: 价格趋势={price_trend:.2f}, RSI趋势={rsi_trend:.2f}, 背离={divergence}")
        return divergence
    except Exception as e:
        print(f"RSI背离检查时出错: {e}")
        return False

def enhanced_signal_confirmation(k2_signal, price_data, signal_type="buy"):
    """
    增强的多重信号确认机制
    核心原则：K2信号是必要条件，其他信号是辅助确认
    """
    if not enable_multi_signal_confirmation:
        return k2_signal

    # 核心逻辑：K2信号是必要条件，没有K2信号就不开仓
    if not k2_signal:
        print(f"多重信号确认 ({signal_type}): K2信号未触发，直接返回False")
        return False

    # K2信号已触发，现在检查辅助确认信号
    auxiliary_confirmations = 0
    confirmation_details = ["K2信号✓"]

    # 1. 成交量确认
    if enable_volume_confirmation and volume_confirmation(price_data):
        auxiliary_confirmations += 1
        confirmation_details.append("成交量确认✓")

    # 2. 价格突破确认
    if check_price_breakout(price_data):
        auxiliary_confirmations += 1
        confirmation_details.append("价格突破✓")

    # 3. RSI背离确认
    if check_rsi_divergence(price_data):
        auxiliary_confirmations += 1
        confirmation_details.append("RSI背离✓")

    # 根据配置的确认模式进行判断
    if signal_confirmation_mode == "strict":
        # 严格模式：K2信号 + 至少1个辅助确认
        if auxiliary_confirmations >= 1:
            result = True
            reason = f"严格模式: K2信号 + {auxiliary_confirmations}个辅助确认"
        else:
            result = False
            reason = "严格模式: K2信号存在但缺乏辅助确认"

    elif signal_confirmation_mode == "loose":
        # 宽松模式：仅K2信号即可
        result = True
        reason = f"宽松模式: K2信号足够 (辅助确认{auxiliary_confirmations}个)"

    else:  # balanced 平衡模式（默认）
        # 平衡模式：K2信号 + 辅助确认或高波动率环境
        if auxiliary_confirmations >= 1:
            result = True
            reason = f"平衡模式: K2信号 + {auxiliary_confirmations}个辅助确认"
        else:
            # 如果没有辅助确认，检查是否在高确定性环境下
            volatility = calculate_realized_volatility(price_data, volatility_lookback_days)
            if volatility > min_volatility_threshold * 1.5:  # 波动率足够高
                result = True
                reason = "平衡模式: K2信号 + 高波动率环境"
            else:
                result = False
                reason = "平衡模式: K2信号存在但缺乏辅助确认且波动率不足"

    print(f"多重信号确认 ({signal_type}): {reason} - {', '.join(confirmation_details)} - 结果: {result}")
    return result

def volatility_filter(price_data):
    """
    波动率过滤器
    """
    if not enable_volatility_filter:
        return True

    volatility = calculate_realized_volatility(price_data, volatility_lookback_days)

    if volatility < min_volatility_threshold:
        print(f"波动率过滤: 当前波动率{volatility:.3f} < 最低阈值{min_volatility_threshold:.3f}, 不适合期权交易")
        return False

    print(f"波动率过滤: 当前波动率{volatility:.3f}, 通过过滤")
    return True

def calculate_dynamic_position_size(option_price, volatility):
    """
    基于波动率的动态仓位计算
    """
    if not enable_dynamic_position_sizing:
        return hand

    try:
        # 基础风险预算（账户的2%）
        risk_budget = g.account_balance * max_single_risk_ratio

        # 波动率调整因子（高波动减仓，低波动加仓）
        vol_adjustment = min(2.0, max(0.5, 0.20 / volatility))

        # 调整后的风险预算
        adjusted_risk = risk_budget * vol_adjustment

        # 计算仓位大小
        position_size = int(adjusted_risk / option_price)

        # 限制在1-5张之间
        final_size = max(1, min(position_size, max_position_size))

        print(f"动态仓位计算: 期权价格={option_price:.2f}, 波动率={volatility:.3f}, "
              f"调整因子={vol_adjustment:.2f}, 建议仓位={final_size}张")

        return final_size
    except Exception as e:
        print(f"动态仓位计算出错: {e}")
        return hand

def check_option_liquidity(ContextInfo, option_code):
    """
    检查期权流动性
    """
    if not enable_liquidity_check:
        return True

    try:
        # 获取期权详细信息
        option_detail = ContextInfo.get_option_detail_data(option_code)

        # 获取买卖价差（这里需要根据实际API调整）
        # 假设可以获取到bid和ask价格
        try:
            bid_price = option_detail.get('BidPrice', 0)
            ask_price = option_detail.get('AskPrice', 0)

            if bid_price > 0 and ask_price > 0:
                spread_ratio = (ask_price - bid_price) / ((ask_price + bid_price) / 2)
                if spread_ratio > max_bid_ask_spread:
                    print(f"流动性检查失败: 买卖价差{spread_ratio:.3f} > 最大允许{max_bid_ask_spread:.3f}")
                    return False
        except:
            pass  # 如果无法获取买卖价差，跳过此检查

        # 检查未平仓合约数（如果API支持）
        try:
            open_interest = option_detail.get('OpenInterest', 0)
            if open_interest < min_open_interest:
                print(f"流动性检查失败: 未平仓合约{open_interest} < 最小要求{min_open_interest}")
                return False
        except:
            pass  # 如果无法获取未平仓数据，跳过此检查

        print(f"流动性检查通过: {option_code}")
        return True

    except Exception as e:
        print(f"流动性检查出错: {e}")
        return True  # 出错时默认通过

def calculate_signal_strength(k2_value, price_data, volume_data=None):
    """
    计算信号强度（0-1），综合多个因素
    """
    try:
        strength = 0.0
        factors = []

        # 1. K2信号强度（基础权重40%）
        k2_strength = min(abs(k2_value) / 5.0, 1.0)  # K2绝对值越大信号越强
        strength += k2_strength * 0.4
        factors.append(f"K2强度:{k2_strength:.2f}")

        # 2. 价格动量强度（权重25%）
        if len(price_data) >= 5:
            recent_returns = price_data['close'].pct_change().tail(5)
            momentum = abs(recent_returns.mean()) * 100  # 转换为百分比
            momentum_strength = min(momentum / 2.0, 1.0)  # 2%为满分
            strength += momentum_strength * 0.25
            factors.append(f"动量:{momentum_strength:.2f}")

        # 3. 波动率环境（权重20%）
        volatility = calculate_realized_volatility(price_data, 10)
        if min_volatility_threshold <= volatility <= high_volatility_threshold:
            vol_strength = 1.0  # 理想波动率环境
        elif volatility < min_volatility_threshold:
            vol_strength = volatility / min_volatility_threshold  # 波动率不足
        else:
            vol_strength = max(0.3, 1.0 - (volatility - high_volatility_threshold) / 0.2)  # 波动率过高
        strength += vol_strength * 0.20
        factors.append(f"波动率:{vol_strength:.2f}")

        # 4. 成交量确认（权重15%）
        volume_strength = 0.5  # 默认中等
        if volume_data is not None and len(volume_data) >= 20:
            avg_volume = volume_data.tail(20).mean()
            current_volume = volume_data.iloc[-1]
            if current_volume > avg_volume * 1.5:
                volume_strength = 1.0
            elif current_volume > avg_volume:
                volume_strength = 0.8
        strength += volume_strength * 0.15
        factors.append(f"成交量:{volume_strength:.2f}")

        print(f"信号强度计算: {strength:.3f} = {' + '.join(factors)}")
        return min(strength, 1.0)

    except Exception as e:
        print(f"信号强度计算出错: {e}")
        return 0.5  # 默认中等强度

def check_market_environment(ContextInfo, price_data):
    """
    检查市场环境是否适合期权交易
    """
    try:
        environment_score = 0
        checks = []

        # 1. 市场成交量检查
        if market_trend_filter:
            # 这里需要获取标的成交量数据，简化处理
            volume_ok = True  # 实际应该检查成交量
            if volume_ok:
                environment_score += 1
                checks.append("成交量✓")
            else:
                checks.append("成交量✗")

        # 2. 价格变动幅度检查
        if len(price_data) >= 2:
            price_change = abs(price_data['close'].iloc[-1] / price_data['close'].iloc[-2] - 1)
            if price_change >= min_price_change_threshold:
                environment_score += 1
                checks.append(f"价格变动{price_change:.3f}✓")
            else:
                checks.append(f"价格变动{price_change:.3f}✗")

        # 3. 波动率环境检查
        volatility = calculate_realized_volatility(price_data, volatility_lookback_days)
        if min_volatility_threshold <= volatility <= high_volatility_threshold * 1.2:
            environment_score += 1
            checks.append(f"波动率{volatility:.3f}✓")
        else:
            checks.append(f"波动率{volatility:.3f}✗")

        # 4. 避免临近到期
        # 这里简化处理，实际应该检查期权到期时间
        environment_score += 1  # 假设通过
        checks.append("到期时间✓")

        result = environment_score >= 3  # 至少3个条件满足
        print(f"市场环境检查: {environment_score}/4 - {', '.join(checks)} - 结果: {result}")
        return result

    except Exception as e:
        print(f"市场环境检查出错: {e}")
        return True  # 出错时默认通过

def check_daily_trade_limit():
    """
    检查每日交易次数限制
    """
    try:
        today = datetime.now().strftime("%Y%m%d")

        # 初始化每日交易计数
        if not hasattr(g, 'daily_trades') or not hasattr(g, 'trade_date'):
            g.daily_trades = 0
            g.trade_date = today

        # 如果是新的一天，重置计数
        if g.trade_date != today:
            g.daily_trades = 0
            g.trade_date = today

        # 检查是否超过限制
        if g.daily_trades >= max_daily_trades:
            print(f"今日交易次数已达上限: {g.daily_trades}/{max_daily_trades}")
            return False

        return True

    except Exception as e:
        print(f"交易次数检查出错: {e}")
        return True

def check_cooldown_period():
    """
    检查亏损后冷静期
    """
    try:
        if not hasattr(g, 'last_loss_time'):
            return True

        if g.last_loss_time is None:
            return True

        time_since_loss = time.time() - g.last_loss_time
        if time_since_loss < cooldown_after_loss:
            remaining = int(cooldown_after_loss - time_since_loss)
            print(f"亏损后冷静期: 还需等待{remaining}秒")
            return False

        return True

    except Exception as e:
        print(f"冷静期检查出错: {e}")
        return True

def check_consecutive_losses():
    """
    检查连续亏损次数限制
    """
    try:
        if not hasattr(g, 'consecutive_losses'):
            g.consecutive_losses = 0

        if g.consecutive_losses >= max_consecutive_losses:
            print(f"连续亏损次数达到上限: {g.consecutive_losses}/{max_consecutive_losses}")
            return False

        return True

    except Exception as e:
        print(f"连续亏损检查出错: {e}")
        return True

def update_trade_result(is_profit):
    """
    更新交易结果，用于连续亏损统计
    """
    try:
        if not hasattr(g, 'consecutive_losses'):
            g.consecutive_losses = 0

        if is_profit:
            g.consecutive_losses = 0  # 盈利时重置连续亏损计数
            print("交易盈利，重置连续亏损计数")
        else:
            g.consecutive_losses += 1
            print(f"交易亏损，连续亏损次数: {g.consecutive_losses}")

            # 如果达到连续亏损上限，启动更长的冷静期
            if g.consecutive_losses >= max_consecutive_losses:
                g.last_loss_time = time.time()
                print(f"连续亏损{max_consecutive_losses}次，启动强制冷静期")

    except Exception as e:
        print(f"交易结果更新出错: {e}")

# ==================== 全新专业期权开仓策略函数 ====================

def get_vix_level():
    """
    获取VIX恐慌指数水平（模拟）
    实际应该从数据源获取真实VIX数据
    """
    try:
        # 这里应该从实际数据源获取VIX
        # 暂时用随机数模拟，实际使用时需要替换
        import random
        simulated_vix = random.uniform(12, 35)
        return simulated_vix
    except:
        return 20  # 默认中性水平

def calculate_iv_percentile(current_iv, historical_iv_data, lookback_days=252):
    """
    计算隐含波动率分位数
    """
    try:
        if len(historical_iv_data) < lookback_days:
            return 50  # 数据不足时返回中位数

        recent_iv = historical_iv_data[-lookback_days:]
        percentile = (recent_iv < current_iv).sum() / len(recent_iv) * 100
        return percentile
    except:
        return 50

def calculate_iv_hv_ratio(implied_vol, realized_vol):
    """
    计算隐含波动率与历史波动率比值
    """
    try:
        if realized_vol <= 0:
            return 1.0
        return implied_vol / realized_vol
    except:
        return 1.0

def analyze_market_microstructure(option_data):
    """
    分析期权市场微观结构
    """
    try:
        analysis = {
            'bid_ask_spread': 0.03,  # 模拟买卖价差
            'open_interest': 500,    # 模拟持仓量
            'daily_volume': 200,     # 模拟日成交量
            'liquidity_score': 0.8   # 模拟流动性评分
        }

        # 实际应该从期权数据中计算
        # bid_ask_spread = (ask_price - bid_price) / mid_price
        # 等等...

        return analysis
    except Exception as e:
        print(f"市场微观结构分析出错: {e}")
        return {
            'bid_ask_spread': 0.05,
            'open_interest': 100,
            'daily_volume': 50,
            'liquidity_score': 0.5
        }

def calculate_trend_strength(price_data, lookback=20):
    """
    计算趋势强度（0-1）
    """
    try:
        if len(price_data) < lookback:
            return 0.5

        # 1. 价格趋势方向一致性
        returns = price_data['close'].pct_change().dropna()
        recent_returns = returns.tail(lookback)

        # 计算趋势一致性
        positive_days = (recent_returns > 0).sum()
        trend_consistency = abs(positive_days - lookback/2) / (lookback/2)

        # 2. 移动平均线排列
        ma5 = price_data['close'].rolling(5).mean().iloc[-1]
        ma10 = price_data['close'].rolling(10).mean().iloc[-1]
        ma20 = price_data['close'].rolling(20).mean().iloc[-1]

        current_price = price_data['close'].iloc[-1]

        # 多头排列得分
        if current_price > ma5 > ma10 > ma20:
            ma_score = 1.0
        elif current_price < ma5 < ma10 < ma20:
            ma_score = 1.0
        else:
            ma_score = 0.3

        # 3. 价格动量
        momentum = abs(recent_returns.mean()) * 100
        momentum_score = min(momentum / 2.0, 1.0)

        # 综合趋势强度
        trend_strength = (trend_consistency * 0.4 + ma_score * 0.4 + momentum_score * 0.2)

        print(f"趋势强度分析: 一致性={trend_consistency:.2f}, MA排列={ma_score:.2f}, 动量={momentum_score:.2f}, 综合={trend_strength:.2f}")
        return trend_strength

    except Exception as e:
        print(f"趋势强度计算出错: {e}")
        return 0.5

def detect_price_breakout(price_data, volume_data=None, lookback=20):
    """
    检测价格突破信号
    """
    try:
        if len(price_data) < lookback + 5:
            return False, 0

        # 1. 布林带突破
        close_prices = price_data['close']
        ma20 = close_prices.rolling(lookback).mean()
        std20 = close_prices.rolling(lookback).std()

        upper_band = ma20 + (std20 * 2)
        lower_band = ma20 - (std20 * 2)

        current_price = close_prices.iloc[-1]
        prev_price = close_prices.iloc[-2]

        # 检测突破
        breakout_up = (current_price > upper_band.iloc[-1]) and (prev_price <= upper_band.iloc[-2])
        breakout_down = (current_price < lower_band.iloc[-1]) and (prev_price >= lower_band.iloc[-2])

        # 2. 成交量确认
        volume_confirmed = True
        if volume_data is not None and len(volume_data) >= lookback:
            avg_volume = volume_data.rolling(lookback).mean().iloc[-1]
            current_volume = volume_data.iloc[-1]
            volume_confirmed = current_volume > avg_volume * volume_surge_ratio

        # 3. 突破强度
        if breakout_up:
            breakout_strength = (current_price - upper_band.iloc[-1]) / std20.iloc[-1]
            direction = 1
        elif breakout_down:
            breakout_strength = (lower_band.iloc[-1] - current_price) / std20.iloc[-1]
            direction = -1
        else:
            breakout_strength = 0
            direction = 0

        # 突破有效性判断
        valid_breakout = (
            (breakout_up or breakout_down) and
            volume_confirmed and
            breakout_strength >= breakout_threshold
        )

        print(f"突破检测: 方向={direction}, 强度={breakout_strength:.2f}, 成交量确认={volume_confirmed}, 有效={valid_breakout}")
        return valid_breakout, direction

    except Exception as e:
        print(f"突破检测出错: {e}")
        return False, 0

def calculate_price_acceleration(price_data, lookback=5):
    """
    计算价格加速度
    """
    try:
        if len(price_data) < lookback + 2:
            return 0

        returns = price_data['close'].pct_change().dropna()
        recent_returns = returns.tail(lookback)

        # 计算加速度（二阶导数）
        velocity = recent_returns.rolling(2).mean()
        acceleration = velocity.diff().iloc[-1]

        return abs(acceleration) if not pd.isna(acceleration) else 0

    except Exception as e:
        print(f"价格加速度计算出错: {e}")
        return 0

def professional_entry_signal_analysis(price_5m, price_30m, volume_data=None):
    """
    专业期权开仓信号分析 - 完全重构的开仓逻辑
    基于波动率驱动 + 技术分析 + 市场微观结构
    """
    try:
        print("=== 专业期权开仓信号分析 ===")

        # 1. 波动率环境分析
        volatility_analysis = analyze_volatility_environment(price_5m)
        if not volatility_analysis['suitable_for_options']:
            print("波动率环境不适合期权交易")
            return False, False, "波动率环境不佳"

        # 2. 趋势强度分析
        trend_strength = calculate_trend_strength(price_5m, momentum_lookback)
        if trend_strength < trend_strength_threshold:
            print(f"趋势强度不足: {trend_strength:.2f} < {trend_strength_threshold}")
            return False, False, "趋势强度不足"

        # 3. 突破信号检测
        breakout_detected, breakout_direction = detect_price_breakout(price_5m, volume_data)
        if not breakout_detected:
            print("未检测到有效突破信号")
            return False, False, "无突破信号"

        # 4. 价格加速度确认
        acceleration = calculate_price_acceleration(price_5m)
        if acceleration < price_acceleration_min:
            print(f"价格加速度不足: {acceleration:.4f} < {price_acceleration_min}")
            return False, False, "价格加速度不足"

        # 5. 多时间框架确认
        trend_5m = calculate_trend_strength(price_5m, 10)
        trend_30m = calculate_trend_strength(price_30m, 10)

        # 要求5分钟和30分钟趋势方向一致
        if (breakout_direction > 0 and trend_30m < 0.6) or (breakout_direction < 0 and trend_30m < 0.6):
            print("多时间框架趋势不一致")
            return False, False, "多时间框架不一致"

        # 6. 市场微观结构检查
        microstructure = analyze_market_microstructure(None)
        if (microstructure['bid_ask_spread'] > bid_ask_spread_max or
            microstructure['liquidity_score'] < liquidity_score_min):
            print("市场微观结构不佳")
            return False, False, "流动性不足"

        # 7. 生成最终信号
        buy_signal = breakout_direction > 0
        sell_signal = breakout_direction < 0

        confidence_score = (
            volatility_analysis['score'] * 0.25 +
            trend_strength * 0.25 +
            min(acceleration / price_acceleration_min, 1.0) * 0.25 +
            microstructure['liquidity_score'] * 0.25
        )

        signal_reason = f"突破方向:{breakout_direction}, 趋势强度:{trend_strength:.2f}, 加速度:{acceleration:.4f}, 置信度:{confidence_score:.2f}"

        print(f"专业信号分析完成: 买入={buy_signal}, 卖出={sell_signal}")
        print(f"信号原因: {signal_reason}")

        return buy_signal, sell_signal, signal_reason

    except Exception as e:
        print(f"专业信号分析出错: {e}")
        return False, False, "分析出错"

def analyze_volatility_environment(price_data):
    """
    分析波动率环境是否适合期权交易
    """
    try:
        # 1. 计算实现波动率
        realized_vol = calculate_realized_volatility(price_data, volatility_lookback_days)

        # 2. 模拟隐含波动率数据（实际应该从期权链获取）
        implied_vol = realized_vol * 1.1  # 简化处理

        # 3. 获取VIX水平
        vix_level = get_vix_level()

        # 4. 计算IV分位数（模拟）
        historical_iv = [realized_vol * (1 + (i-126)/1000) for i in range(252)]  # 模拟历史IV
        iv_percentile = calculate_iv_percentile(implied_vol, historical_iv)

        # 5. 计算IV/HV比值
        iv_hv_ratio = calculate_iv_hv_ratio(implied_vol, realized_vol)

        # 6. 综合评估
        analysis = {
            'realized_vol': realized_vol,
            'implied_vol': implied_vol,
            'vix_level': vix_level,
            'iv_percentile': iv_percentile,
            'iv_hv_ratio': iv_hv_ratio,
            'suitable_for_options': False,
            'score': 0.0,
            'reasons': []
        }

        score = 0.0
        reasons = []

        # 实现波动率检查
        if realized_vol_min <= realized_vol <= realized_vol_max:
            score += 0.3
            reasons.append(f"实现波动率适中({realized_vol:.3f})")
        else:
            reasons.append(f"实现波动率异常({realized_vol:.3f})")

        # VIX水平检查
        if vix_threshold_low <= vix_level <= vix_threshold_high:
            score += 0.2
            reasons.append(f"VIX水平适中({vix_level:.1f})")
        else:
            reasons.append(f"VIX水平异常({vix_level:.1f})")

        # IV分位数检查
        if iv_percentile_buy_min <= iv_percentile <= iv_percentile_buy_max:
            score += 0.3
            reasons.append(f"IV分位数适中({iv_percentile:.1f}%)")
        else:
            reasons.append(f"IV分位数不佳({iv_percentile:.1f}%)")

        # IV/HV比值检查
        if 0.8 <= iv_hv_ratio <= 1.5:
            score += 0.2
            reasons.append(f"IV/HV比值合理({iv_hv_ratio:.2f})")
        else:
            reasons.append(f"IV/HV比值异常({iv_hv_ratio:.2f})")

        analysis['score'] = score
        analysis['reasons'] = reasons
        analysis['suitable_for_options'] = score >= 0.6  # 至少60%得分才适合

        print(f"波动率环境分析: 得分={score:.2f}, 适合期权={analysis['suitable_for_options']}")
        print(f"分析详情: {', '.join(reasons)}")

        return analysis

    except Exception as e:
        print(f"波动率环境分析出错: {e}")
        return {
            'suitable_for_options': False,
            'score': 0.0,
            'reasons': ['分析出错']
        }

def init(ContextInfo):
    g.remark = ContextInfo.request_id[-10:]
    g.call_one = None
    g.put_one = None
    ContextInfo.set_account(account)  # 设置账户ID，确保account已定义
    g.undl_code = g.code = g.stock = ContextInfo.stockcode + '.' + ContextInfo.market  # 设置标的代码和市场信息，确保g.undl_code已定义
    g.curr_hold = None

def after_init(ContextInfo):
    # 下载历史数据（如果需要的话）
    try:
        # 注释掉download_history_data，因为这个函数可能不存在
        # download_history_data(g.code, '1d', '********', '********')
        pass
    except Exception as e:
        print(f"下载历史数据时出错: {e}")

    # 返回交易日期信息
    try:
        return ContextInfo.get_trading_dates('SHO', '', '********', count=2, period='1d')
    except Exception as e:
        print(f"获取交易日期时出错: {e}")
        return None, None



def calculate_wenhua_lines(price_data):
    """严格按照文华指标计算红白线值"""
    if len(price_data) < 10:  # 需要足够的历史数据
        return 0, 0, 0
        
    # 获取数据
    high = price_data['high'].values
    low = price_data['low'].values
    open_price = price_data['open'].values
    close = price_data['close'].values
    
    # 初始化结果数组
    h1 = np.zeros(len(price_data))
    l1 = np.zeros(len(price_data))
    h2 = np.zeros(len(price_data))
    l2 = np.zeros(len(price_data))
    k1 = np.zeros(len(price_data))
    k2 = np.zeros(len(price_data))
    g = np.zeros(len(price_data))
    
    # 计算HX和LX - 修正为正确的2周期最高最低价
    # 文华指标：HX:=HHV(HIGH,2); LX:=LLV(LOW,2);
    # HHV(HIGH,2)表示包含当前K线在内的2个周期的最高价
    hx = np.zeros(len(price_data))
    lx = np.zeros(len(price_data))

    for i in range(len(price_data)):
        if i >= 1:
            # 包含当前K线在内的2个周期：[i-1, i]
            hx[i] = max(high[i-1:i+1])  # HHV(HIGH,2)
            lx[i] = min(low[i-1:i+1])   # LLV(LOW,2)
        else:
            # 第一根K线时，只有1个周期的数据
            hx[i] = high[i]
            lx[i] = low[i]
    
    # 计算H1和L1
    for i in range(5, len(price_data)):
        # H1条件: IFELSE(HX<REF(HX,1)&&HX<REF(HX,2)&&HX<REF(HX,4)&&LX<REF(LX,1)&&LX<REF(LX,3)&&LX<REF(LX,5)&&OPEN>CLOSE&&(HHV(OPEN,0)-CLOSE)>0,REF(HX,4),0)
        h1_condition = (
            hx[i] < hx[i-1] and 
            hx[i] < hx[i-2] and 
            hx[i] < hx[i-4] and 
            lx[i] < lx[i-1] and 
            lx[i] < lx[i-3] and 
            lx[i] < lx[i-5] and 
            open_price[i] > close[i] and 
            (max(open_price[:i+1]) - close[i]) > 0
        )
        
        if h1_condition:
            h1[i] = hx[i-4]  # REF(HX,4)
        
        # L1条件: IFELSE(LX>REF(LX,1)&&LX>REF(LX,3)&&LX>REF(LX,5)&&HX>REF(HX,1)&&HX>REF(HX,2)&&HX>REF(HX,4)&&OPEN<CLOSE&&(CLOSE-LLV(OPEN,0))>0,REF(LX,4),0)
        l1_condition = (
            lx[i] > lx[i-1] and 
            lx[i] > lx[i-3] and 
            lx[i] > lx[i-5] and 
            hx[i] > hx[i-1] and 
            hx[i] > hx[i-2] and 
            hx[i] > hx[i-4] and 
            open_price[i] < close[i] and 
            (close[i] - min(open_price[:i+1])) > 0
        )
        
        if l1_condition:
            l1[i] = lx[i-4]  # REF(LX,4)
    
    # 计算H2和L2 (VALUEWHEN函数)
    last_h1 = 0
    last_l1 = 0
    for i in range(len(price_data)):
        if h1[i] > 0:
            last_h1 = h1[i]
        h2[i] = last_h1
        
        if l1[i] > 0:
            last_l1 = l1[i]
        l2[i] = last_l1
    
    # 计算K1
    for i in range(len(price_data)):
        if h2[i] > 0 and close[i] > h2[i]:
            k1[i] = -3
        elif l2[i] > 0 and close[i] < l2[i]:
            k1[i] = 1
        else:
            k1[i] = 0
    
    # 计算K2 (VALUEWHEN函数)
    last_k1 = 0
    for i in range(len(price_data)):
        if k1[i] != 0:
            last_k1 = k1[i]
        k2[i] = last_k1
    
    # 计算G
    for i in range(len(price_data)):
        if k2[i] == 1:
            g[i] = h2[i]  # G:=IFELSE(K2=1,H2,L2)
        else:
            g[i] = l2[i]
    
    # 获取最新值
    latest_k2 = k2[-1]
    latest_g = g[-1]
    
    # 根据K2值确定显示的颜色
    white_line = latest_g if latest_k2 == 1 else None
    red_line = latest_g if latest_k2 == -3 else None
    
    # 打印计算过程
    print("\n文华指标计算过程:")
    print(f"1. 基础数据:")
    print(f"   最新开盘价: {open_price[-1]:.4f}")
    print(f"   最新收盘价: {close[-1]:.4f}")
    print(f"   2周期最高价HX: {hx[-1]:.4f}")
    print(f"   2周期最低价LX: {lx[-1]:.4f}")
    print(f"2. H1和L1值:")
    print(f"   H1: {h1[-1]:.4f}")
    print(f"   L1: {l1[-1]:.4f}")
    print(f"3. H2和L2值:")
    print(f"   H2: {h2[-1]:.4f}")
    print(f"   L2: {l2[-1]:.4f}")
    print(f"4. K1和K2值:")
    print(f"   K1: {k1[-1]}")
    print(f"   K2: {latest_k2}")
    print(f"5. G值: {latest_g:.4f}")
    print(f"6. 当前显示:")
    print(f"   白色线: {f'{white_line:.4f}' if white_line is not None else 'None'}")
    print(f"   红色线: {f'{red_line:.4f}' if red_line is not None else 'None'}")
    
    return white_line, red_line, latest_k2

def handlebar(ContextInfo):
    # 获取当前时间
    current_time = str(ContextInfo.barpos)  # 确保是字符串类型
    
    # 移除尾盘强制平仓相关逻辑，保留时间止损
    # 原有如下代码需删除：
    # if current_time >= '145400': ...
    # if current_time >= '145000': ...
    # if g.hold == 1 and current_time >= '145500': ...
    # if g.hold == -1 and current_time >= '145500': ...
    # 直接跳过这些时间段判断和强平逻辑

    timetag = ContextInfo.get_bar_timetag(ContextInfo.barpos)
    bar_date = str(timetag)
    
    if not ContextInfo.is_last_bar():
        return

    current_date = time.strftime("%Y%m%d")  # 获取当前日期
    current_weekday = datetime.now().weekday()  # 获取当前星期几

    # 检查是否为交易日（假设周一到周五为交易日）
    if current_weekday >= 5:  # 0=周一, 1=周二, ..., 6=周日
        print("今天是周末，市场休市，无法生成信号。")
        return

    start, end = after_init(ContextInfo)
    
    while True:  # 循环等待数据
        try:
            # 获取当前时间
            current_time = time.strftime("%H%M%S")
            
            # 修改获取K线数据的方式，添加'open'字段
            yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y%m%d")
            price_5m = ContextInfo.get_market_data_ex(
                ['close', 'high', 'low', 'open'],  # 添加'open'字段
                [g.code], 
                period='5m',
                start_time=yesterday + '090000',
                end_time=current_date + '150000'
            )[g.code]
            
            # 获取30分钟K线数据时也添加'open'字段
            last_day_price_30m = ContextInfo.get_market_data_ex(
                ['close', 'high', 'low', 'open'],  # 添加'open'字段
                [g.code], 
                period='30m',
                start_time=yesterday + '140000',
                end_time=yesterday + '150000'
            )[g.code]

            today_price_30m = ContextInfo.get_market_data_ex(
                ['close', 'high', 'low', 'open'],  # 添加'open'字段
                [g.code], 
                period='30m',
                start_time=current_date + '090000',
                end_time=current_date + '150000'
            )[g.code]

            # 合并昨天和今天的30分钟K线数据
            if not last_day_price_30m.empty:
                price_30m = pd.concat([last_day_price_30m.iloc[-1:], today_price_30m])
                print(f"成功获取昨日最后一根30分钟K线，时间: {last_day_price_30m.index[-1]}")
            else:
                price_30m = today_price_30m
                print("未能获取昨日30分钟K线数据")

            # 打印获取到的数据信息
            print(f"当前时间: {current_time}")
            print(f"5分钟数据行数: {len(price_5m) if not price_5m.empty else 0}")
            print(f"30分钟数据行数: {len(price_30m) if not price_30m.empty else 0}")

            # 根据时间段来判断是否需要检查K线数量
            if current_time >= '100000':  # 10:00以后才检查30分钟K线数量
                if len(price_5m) < 2:  # 至少需要2根5分钟K线
                    print("5分钟数据不足，等待更多数据")
                    time.sleep(120)  # 等待120秒后重试
                    continue  # 继续循环，重新获取数据
                    
                if len(price_30m) < 2:  # 至少需要2根30分钟K线
                    print("30分钟数据不足，等待更多数据")
                    time.sleep(120)  # 等待120秒后重试
                    continue  # 继续循环，重新获取数据
            else:
                # 开盘初期，只检查5分钟K线
                if len(price_5m) < 2:
                    print("等待更多5分钟K线数据...")
                    time.sleep(60)  # 等待60秒后重试
                    continue
                # 开盘初期，如果30分钟数据不足，只使用5分钟信号
                if len(price_30m) < 2:
                    print("开盘初期，30分钟数据不足，仅使用5分钟K线信号")
                else:
                    print("开盘初期，使用5分钟和30分钟K线数据")

            break  # 数据足够，退出循环

        except Exception as e:
            print(f"获取K线数据时发生错误: {str(e)}")
            return

    # 检查数据是否为空
    if price_5m.empty:
        print("5分钟数据为空，无法生成信号")
    else:
        print("成功获取5分钟数据")
        print(f"5分钟数据时间范围: {price_5m.index[0]} 到 {price_5m.index[-1]}")

    if price_30m.empty:
        print("30分钟数据为空，无法生成信号")
    else:
        print("成功获取30分钟数据")
        print(f"30分钟数据时间范围: {price_30m.index[0]} 到 {price_30m.index[-1]}")

    if price_5m.empty or price_30m.empty:
        print("5分钟或30分钟数据为空，无法生成信号")
        return

    # 计算文华指标红白线值
    white_line_5m, red_line_5m, k2_5m = calculate_wenhua_lines(price_5m)
    white_line_30m, red_line_30m, k2_30m = calculate_wenhua_lines(price_30m)

    # 计算55日均线
    MA55 = MA(price_5m['close'], 55)
    latest_ma55 = MA55[-1] if len(MA55) > 0 else 0

    # 获取最新收盘价
    latest_close_5m = price_5m['close'][-1]
    latest_close_30m = price_30m['close'][-1]

    # 计算K2序列，严格用前一根K2和当前K2判断信号
    def get_k2_series(price_data):
        """计算完整的K2序列"""
        if len(price_data) < 10:
            return []
        
        # 获取数据
        high = price_data['high'].values
        low = price_data['low'].values
        open_price = price_data['open'].values
        close = price_data['close'].values
        
        # 初始化结果数组
        h1 = np.zeros(len(price_data))
        l1 = np.zeros(len(price_data))
        h2 = np.zeros(len(price_data))
        l2 = np.zeros(len(price_data))
        k1 = np.zeros(len(price_data))
        k2 = np.zeros(len(price_data))
        
        # 计算HX和LX
        hx = np.zeros(len(price_data))
        lx = np.zeros(len(price_data))
        
        for i in range(len(price_data)):
            if i >= 1:
                hx[i] = max(high[i-1:i+1])  # HHV(HIGH,2)
                lx[i] = min(low[i-1:i+1])   # LLV(LOW,2)
            else:
                hx[i] = high[i]
                lx[i] = low[i]
        
        # 计算H1和L1
        for i in range(5, len(price_data)):
            # H1条件
            h1_condition = (
                hx[i] < hx[i-1] and 
                hx[i] < hx[i-2] and 
                hx[i] < hx[i-4] and 
                lx[i] < lx[i-1] and 
                lx[i] < lx[i-3] and 
                lx[i] < lx[i-5] and 
                open_price[i] > close[i] and 
                (max(open_price[:i+1]) - close[i]) > 0
            )
            
            if h1_condition:
                h1[i] = hx[i-4]  # REF(HX,4)
            
            # L1条件
            l1_condition = (
                lx[i] > lx[i-1] and 
                lx[i] > lx[i-3] and 
                lx[i] > lx[i-5] and 
                hx[i] > hx[i-1] and 
                hx[i] > hx[i-2] and 
                hx[i] > hx[i-4] and 
                open_price[i] < close[i] and 
                (close[i] - min(open_price[:i+1])) > 0
            )
            
            if l1_condition:
                l1[i] = lx[i-4]  # REF(LX,4)
        
        # 计算H2和L2 (VALUEWHEN函数)
        last_h1 = 0
        last_l1 = 0
        for i in range(len(price_data)):
            if h1[i] > 0:
                last_h1 = h1[i]
            h2[i] = last_h1
            
            if l1[i] > 0:
                last_l1 = l1[i]
            l2[i] = last_l1
        
        # 计算K1
        for i in range(len(price_data)):
            if h2[i] > 0 and close[i] > h2[i]:
                k1[i] = -3
            elif l2[i] > 0 and close[i] < l2[i]:
                k1[i] = 1
            else:
                k1[i] = 0
        
        # 计算K2 (VALUEWHEN函数)
        last_k1 = 0
        for i in range(len(price_data)):
            if k1[i] != 0:
                last_k1 = k1[i]
            k2[i] = last_k1
        
        return k2.tolist()

    k2_series_5m = get_k2_series(price_5m)
    k2_series_30m = get_k2_series(price_30m) if len(price_30m) >= 10 else []

    prev_k2_5m = k2_series_5m[-2] if len(k2_series_5m) > 1 else 0
    k2_5m = k2_series_5m[-1] if k2_series_5m else 0
    prev_k2_30m = k2_series_30m[-2] if len(k2_series_30m) > 1 else 0
    k2_30m = k2_series_30m[-1] if k2_series_30m else 0

    # 根据文华指标源代码修正信号检测逻辑
    # 原文华代码：DRAWTEXT(CROSS(TMP,0),HX,'卖') - TMP从负数变为0时显示"卖"
    # 原文华代码：DRAWTEXT(CROSS(0,TMP),LX,'买') - TMP从0变为正数时显示"买"
    # 其中TMP=K2，所以：
    # 买信号：K2从非正数变为1 (CROSS(0,TMP) 即 TMP从<=0变为>0)
    # 卖信号：K2从非零变为0或从正数变为负数 (CROSS(TMP,0) 即 TMP从>0变为<=0)

    # 但根据文华指标的实际逻辑，更准确的理解是：
    # CROSS(0,TMP): TMP从0变为非0正值（即K2从0变为1）
    # CROSS(TMP,0): TMP从非0值变为0（但K2不会变为0，而是保持上一个非0值）

    # 因此正确的信号检测应该是：
    if enable_ma55_filter:
        # 买信号：K2从非1变为1，且收盘价>=MA55 (对应文华的CROSS(0,TMP)&&CLOSE>=MA55)
        k2_buy_signal_5m = (prev_k2_5m != 1 and k2_5m == 1 and latest_close_5m >= latest_ma55)
        # 卖信号：K2从非-3变为-3，且收盘价<=MA55 (对应文华的CROSS(TMP,0)&&CLOSE<=MA55)
        k2_sell_signal_5m = (prev_k2_5m != -3 and k2_5m == -3 and latest_close_5m <= latest_ma55)
        k2_buy_signal_30m = (prev_k2_30m != 1 and k2_30m == 1 and latest_close_30m >= latest_ma55) if len(price_30m) >= 10 else False
        k2_sell_signal_30m = (prev_k2_30m != -3 and k2_30m == -3 and latest_close_30m <= latest_ma55) if len(price_30m) >= 10 else False
    else:
        # 不使用MA55过滤时的信号检测
        k2_buy_signal_5m = (prev_k2_5m != 1 and k2_5m == 1)
        k2_sell_signal_5m = (prev_k2_5m != -3 and k2_5m == -3)
        k2_buy_signal_30m = (prev_k2_30m != 1 and k2_30m == 1) if len(price_30m) >= 10 else False
        k2_sell_signal_30m = (prev_k2_30m != -3 and k2_30m == -3) if len(price_30m) >= 10 else False
    
    # ==================== 全新专业期权开仓策略 ====================

    # 方案A：完全使用新的专业策略（推荐）
    if strategy_mode == "adaptive":
        print("使用全新专业期权开仓策略")

        # 获取成交量数据（如果可用）
        volume_data = None
        try:
            # 这里应该获取实际成交量数据
            # volume_data = get_volume_data(...)
            pass
        except:
            pass

        # 专业信号分析
        professional_buy, professional_sell, signal_reason = professional_entry_signal_analysis(
            price_5m, price_30m, volume_data
        )

        # 风险控制检查
        risk_checks_passed = True
        risk_results = []

        # 1. 每日交易次数限制
        trade_limit_ok = check_daily_trade_limit()
        risk_checks_passed = risk_checks_passed and trade_limit_ok
        risk_results.append(f"交易次数:{trade_limit_ok}")

        # 2. 亏损后冷静期
        cooldown_ok = check_cooldown_period()
        risk_checks_passed = risk_checks_passed and cooldown_ok
        risk_results.append(f"冷静期:{cooldown_ok}")

        # 3. 连续亏损检查
        consecutive_loss_ok = check_consecutive_losses()
        risk_checks_passed = risk_checks_passed and consecutive_loss_ok
        risk_results.append(f"连续亏损:{consecutive_loss_ok}")

        # 最终专业信号
        buy_signal = professional_buy and risk_checks_passed
        sell_signal = professional_sell and risk_checks_passed

        print(f"=== 专业期权策略信号 ===")
        print(f"专业分析: 买入={professional_buy}, 卖出={professional_sell}")
        print(f"信号原因: {signal_reason}")
        print(f"风险检查: {', '.join(risk_results)}")
        print(f"最终信号: 买入={buy_signal}, 卖出={sell_signal}")
        print(f"========================")

    else:
        # 方案B：保留原有K2策略作为备选（保守模式）
        print("使用传统K2信号策略")

        # 基础K2信号
        basic_buy_signal = k2_buy_signal_5m or k2_buy_signal_30m
        basic_sell_signal = k2_sell_signal_5m or k2_sell_signal_30m

        # 简化的过滤条件
        filters_passed = True
        filter_results = []

        # 1. 波动率过滤
        vol_filter_pass = volatility_filter(price_5m)
        filters_passed = filters_passed and vol_filter_pass
        filter_results.append(f"波动率:{vol_filter_pass}")

        # 2. 每日交易次数限制
        trade_limit_ok = check_daily_trade_limit()
        filters_passed = filters_passed and trade_limit_ok
        filter_results.append(f"交易次数:{trade_limit_ok}")

        # 3. 亏损后冷静期
        cooldown_ok = check_cooldown_period()
        filters_passed = filters_passed and cooldown_ok
        filter_results.append(f"冷静期:{cooldown_ok}")

        # 增强信号确认
        buy_confirmed = basic_buy_signal and enhanced_signal_confirmation(basic_buy_signal, price_5m, "buy")
        sell_confirmed = basic_sell_signal and enhanced_signal_confirmation(basic_sell_signal, price_5m, "sell")

        # 最终信号
        buy_signal = buy_confirmed and filters_passed
        sell_signal = sell_confirmed and filters_passed

        print(f"=== 传统K2策略信号 ===")
        print(f"基础信号: 买入={basic_buy_signal}, 卖出={basic_sell_signal}")
        print(f"信号确认: 买入={buy_confirmed}, 卖出={sell_confirmed}")
        print(f"过滤条件: {', '.join(filter_results)}")
        print(f"最终信号: 买入={buy_signal}, 卖出={sell_signal}")
        print(f"=====================")

    # 打印K2跳变、收盘价、MA55的值，便于分析信号被过滤的原因
    print(f"K2跳变检测: prev_k2_5m={prev_k2_5m}, k2_5m={k2_5m}, latest_close_5m={latest_close_5m:.4f}, latest_ma55={latest_ma55:.4f}")
    print(f"K2跳变检测: prev_k2_30m={prev_k2_30m}, k2_30m={k2_30m}, latest_close_30m={latest_close_30m:.4f}, latest_ma55={latest_ma55:.4f}")

    # 更新prev_k2
    g.prev_k2_5m = k2_5m
    g.prev_k2_30m = k2_30m

    # 打印K2信号检测结果
    print(f"K2值变化检测: 5分钟前值={g.prev_k2_5m}, 当前值={k2_5m}, 30分钟前值={g.prev_k2_30m}, 当前值={k2_30m}")
    print(f"30分钟数据充足性: {len(price_30m) >= 10}")
    if k2_buy_signal_5m or k2_buy_signal_30m:
        print(f"K2看涨信号触发: 5分钟={k2_buy_signal_5m}, 30分钟={k2_buy_signal_30m}")
    if k2_sell_signal_5m or k2_sell_signal_30m:
        print(f"K2看跌信号触发: 5分钟={k2_sell_signal_5m}, 30分钟={k2_sell_signal_30m}")
    
    if buy_signal:
        print(f"\n触发买入认购期权信号! (K2看涨信号)")
        # 开多逻辑 - 只有在没有持仓时才开仓
        time_since_last_long = time.time() - g.trace_time_long
        if g.hold == 0 and time_since_last_long > sleep_time * 60:
            # 使用智能合约选择
            call_one = get_option_smart_selection(ContextInfo, 1, price_5m)
            if call_one is None:
                print("警告：无法获取认购期权合约，跳过开仓")
                return

            # 计算动态仓位
            try:
                option_price = ContextInfo.get_full_tick([call_one])[call_one]['lastPrice']
                volatility = calculate_realized_volatility(price_5m, volatility_lookback_days)
                position_size = calculate_dynamic_position_size(option_price, volatility)
            except:
                option_price = 1.0  # 默认价格
                position_size = hand

            g.call_one = call_one
            safe_passorder(50, 1101, account, call_one, 12, 0, position_size, '', 1, '期权策略开多' + g.remark, ContextInfo)
            g.curr_hold = call_one
            g.hold = 1
            g.trace_time_long = time.time()
            g.opened_t.append(bar_date)
            g.hold_price = latest_close_5m
            g.position_size = position_size  # 记录实际仓位大小

            # 更新每日交易计数
            if hasattr(g, 'daily_trades'):
                g.daily_trades += 1
            else:
                g.daily_trades = 1

            print(f'{call_one} 买入认购期权，行权价: {ContextInfo.get_option_detail_data(call_one)["OptExercisePrice"]}, 仓位: {position_size}张')
            print(f'今日交易次数: {g.daily_trades}/{max_daily_trades}')
        else:
            if g.hold != 0:
                print(f"当前持仓状态: {g.hold}，无法开仓")
            else:
                print(f"距离上次开多时间不足{sleep_time}分钟，剩余{int(sleep_time * 60 - time_since_last_long)}秒")
            return

    if sell_signal:
        print(f"\n触发买入认沽期权信号! (K2看跌信号)")
        # 开空逻辑 - 只有在没有持仓时才开仓
        time_since_last_short = time.time() - g.trace_time_short
        if g.hold == 0 and time_since_last_short > sleep_time * 60:
            # 使用智能合约选择
            put_one = get_option_smart_selection(ContextInfo, -1, price_5m)
            if put_one is None:
                print("警告：无法获取认沽期权合约，跳过开仓")
                return

            # 计算动态仓位
            try:
                option_price = ContextInfo.get_full_tick([put_one])[put_one]['lastPrice']
                volatility = calculate_realized_volatility(price_5m, volatility_lookback_days)
                position_size = calculate_dynamic_position_size(option_price, volatility)
            except:
                option_price = 1.0  # 默认价格
                position_size = hand

            g.put_one = put_one
            safe_passorder(50, 1101, account, put_one, 12, 0, position_size, '', 1, '期权策略开空' + g.remark, ContextInfo)
            g.curr_hold = put_one
            g.hold = -1
            g.trace_time_short = time.time()
            g.opened_t.append(bar_date)
            g.hold_price = latest_close_5m
            g.position_size = position_size  # 记录实际仓位大小
            print(f'{put_one} 买入认沽期权，行权价: {ContextInfo.get_option_detail_data(put_one)["OptExercisePrice"]}, 仓位: {position_size}张')
        else:
            if g.hold != 0:
                print(f"当前持仓状态: {g.hold}，无法开仓")
            else:
                print(f"距离上次开空时间不足{sleep_time}分钟，剩余{int(sleep_time * 60 - time_since_last_short)}秒")
            return

    # 检查合约到期日并自动切换
    if g.hold != 0:
        # 每次运行时都检查当前持仓合约的到期日
        try:
            current_contract_detail = ContextInfo.get_option_detail_data(g.curr_hold)
            current_end_date = current_contract_detail['EndDelivDate']
            current_end_date_str = str(current_end_date)
            
            # 计算距离到期日的天数
            end_date_obj = datetime.strptime(current_end_date_str, '%Y%m%d')
            current_date_obj = datetime.now()
            days_to_expiry = (end_date_obj - current_date_obj).days
            
            print(f"合约到期检查: {g.curr_hold}, 到期日: {current_end_date_str}, 距离到期: {days_to_expiry}天")
            
            # 如果距离到期日小于等于设定天数，切换到下个月合约
            if days_to_expiry <= expiry_days_threshold:
                print(f"距离到期日不足{expiry_days_threshold}天，切换到下个月合约")
                # 平仓当前持仓
                if g.hold == 1:  # 持有多头
                    safe_passorder(51, 1101, account, g.curr_hold, 12, 0, hand, '', 1, '到期前平仓认购' + g.remark, ContextInfo)
                    print(f"平仓认购期权: {g.curr_hold}")
                elif g.hold == -1:  # 持有空头
                    safe_passorder(51, 1101, account, g.curr_hold, 12, 0, hand, '', 1, '到期前平仓认沽' + g.remark, ContextInfo)
                    print(f"平仓认沽期权: {g.curr_hold}")
                
                # 重置持仓状态
                g.hold = 0
                g.curr_hold = None
                g.hold_price = 0
                return  # 平仓后直接返回，等待下次信号
                
        except Exception as e:
            print(f"检查合约到期日时出错: {e}")

    # ====== 最佳止盈止损方案实现开始 ======
    if g.hold != 0 and g.curr_hold is not None and g.hold_price > 0:
        # 1. 固定止损
        if g.hold == 1:
            profit_ratio = (latest_close_5m - g.hold_price) / g.hold_price * 100
        else:
            profit_ratio = (g.hold_price - latest_close_5m) / g.hold_price * 100

        # 固定止损参数
        best_stoploss_ratio = -30  # -30%
        # 浮盈追踪止盈参数
        best_trail_start = 50      # +50%启动追踪
        best_trail_drawdown = 20   # 回撤20%止盈
        # 时间止损参数
        best_max_hold_days = hold_days_limit

        # 记录持仓开始时间
        if not hasattr(g, 'open_time') or g.open_time is None:
            g.open_time = datetime.now()
        hold_days = (datetime.now() - g.open_time).days

        # 固定止损
        if profit_ratio <= best_stoploss_ratio:
            print(f'固定止损触发，平仓，盈亏比: {profit_ratio:.2f}%')
            # 使用记录的实际仓位大小
            actual_position = getattr(g, 'position_size', hand)
            safe_passorder(51, 1101, account, g.curr_hold, 12, 0, actual_position, '', 1, '最佳止损平仓' + g.remark, ContextInfo)

            # 记录亏损时间，启动冷静期
            g.last_loss_time = time.time()
            print(f'亏损平仓，启动{cooldown_after_loss}秒冷静期')

            # 更新交易结果统计
            update_trade_result(False)  # 亏损交易

            g.hold = 0
            g.curr_hold = None
            g.hold_price = 0
            g.open_time = None
            g.max_profit_ratio = None
            g.position_size = None  # 重置仓位记录
            return

        # 浮盈追踪止盈
        if not hasattr(g, 'max_profit_ratio') or g.max_profit_ratio is None:
            g.max_profit_ratio = profit_ratio
        else:
            g.max_profit_ratio = max(g.max_profit_ratio, profit_ratio)
        if g.max_profit_ratio >= best_trail_start and profit_ratio <= g.max_profit_ratio - best_trail_drawdown:
            print(f'浮盈追踪止盈触发，平仓，最高浮盈: {g.max_profit_ratio:.2f}%，当前盈亏: {profit_ratio:.2f}%')
            # 使用记录的实际仓位大小
            actual_position = getattr(g, 'position_size', hand)
            safe_passorder(51, 1101, account, g.curr_hold, 12, 0, actual_position, '', 1, '最佳追踪止盈平仓' + g.remark, ContextInfo)

            # 更新交易结果统计
            update_trade_result(True)  # 盈利交易

            g.hold = 0
            g.curr_hold = None
            g.hold_price = 0
            g.open_time = None
            g.max_profit_ratio = None
            g.position_size = None  # 重置仓位记录
            return

        # 时间止损
        if hold_days >= best_max_hold_days:
            print(f'时间止损触发，平仓，持仓天数: {hold_days}天')
            # 使用记录的实际仓位大小
            actual_position = getattr(g, 'position_size', hand)
            safe_passorder(51, 1101, account, g.curr_hold, 12, 0, actual_position, '', 1, '最佳时间止损平仓' + g.remark, ContextInfo)
            g.hold = 0
            g.curr_hold = None
            g.hold_price = 0
            g.open_time = None
            g.max_profit_ratio = None
            g.position_size = None  # 重置仓位记录
            return
    # ====== 最佳止盈止损方案实现结束 ======

    # 基于K2信号变化的止盈止损逻辑
    if g.hold != 0 and g.curr_hold is not None:
        # 检查是否需要平仓并反向开仓
        if g.hold == 1:  # 持有多头（认购期权）
            # 如果出现"卖"字信号（K2从0变为-3），平仓认购并开仓认沽
            if k2_sell_signal_5m or (len(price_30m) >= 10 and k2_sell_signal_30m):
                print(f"\nK2信号变化止盈止损 - 平仓认购期权: {g.curr_hold}")
                safe_passorder(51, 1101, account, g.curr_hold, 12, 0, hand, '', 1, 'K2信号变化平仓认购' + g.remark, ContextInfo)
                g.hold = 0
                g.curr_hold = None
                g.hold_price = 0
                
                # 立即开仓认沽期权
                print(f"K2信号变化 - 开仓认沽期权")
                call_one, put_one = get_option_real_one(ContextInfo)
                if put_one is None:
                    print("警告：无法获取认沽期权合约，跳过反向开仓")
                    return
                g.put_one = put_one
                safe_passorder(50, 1101, account, put_one, 12, 0, hand, '', 1, 'K2信号变化开仓认沽' + g.remark, ContextInfo)
                g.curr_hold = put_one
                g.hold = -1
                g.trace_time_short = time.time()
                g.opened_t.append(bar_date)
                g.hold_price = latest_close_5m
                print(f'{put_one} 买入认沽期权，行权价: {ContextInfo.get_option_detail_data(put_one)["OptExercisePrice"]}')
                
        elif g.hold == -1:  # 持有空头（认沽期权）
            # 如果出现"买"字信号（K2从0变为1），平仓认沽并开仓认购
            if k2_buy_signal_5m or (len(price_30m) >= 10 and k2_buy_signal_30m):
                print(f"\nK2信号变化止盈止损 - 平仓认沽期权: {g.curr_hold}")
                safe_passorder(51, 1101, account, g.curr_hold, 12, 0, hand, '', 1, 'K2信号变化平仓认沽' + g.remark, ContextInfo)
                g.hold = 0
                g.curr_hold = None
                g.hold_price = 0
                
                # 立即开仓认购期权
                print(f"K2信号变化 - 开仓认购期权")
                call_one, put_one = get_option_real_one(ContextInfo)
                if call_one is None:
                    print("警告：无法获取认购期权合约，跳过反向开仓")
                    return
                g.call_one = call_one
                safe_passorder(50, 1101, account, call_one, 12, 0, hand, '', 1, 'K2信号变化开仓认购' + g.remark, ContextInfo)
                g.curr_hold = call_one
                g.hold = 1
                g.trace_time_long = time.time()
                g.opened_t.append(bar_date)
                g.hold_price = latest_close_5m
                print(f'{call_one} 买入认购期权，行权价: {ContextInfo.get_option_detail_data(call_one)["OptExercisePrice"]}')



# 辅助函数
def get_shape(CG, FL, MA_Line, FS, B1, B2):
    count = 0
    record = []
    compare_ma = []
    for cg, fl, ma, fs, b1, b2 in zip(CG, FL, MA_Line, FS, B1, B2):
        if math.isnan(cg) or math.isnan(fl) or math.isnan(fs):
            continue
        record.append(cg == fl == fs)
        compare_ma.append(min(cg, fl, fs) > ma)
    pre = None
    record.reverse()
    compare_ma.reverse()
    i = 0
    if not record:
        return 0, 99
    if not record[0]:
        return 0, 99
    if not compare_ma[0]:
        return 0, 99
    uprecord = []
    for r, cpma in zip(record, compare_ma):
        if not cpma:
            break
        uprecord.append(r)
    drop_uprecord = []
    for i in range(len(uprecord)):
        if i == 0 or uprecord[i] != uprecord[i - 1]:
            drop_uprecord.append(uprecord[i])
    if drop_uprecord.count(False) != 1:
        return 0, 99
    else:
        return 1, uprecord.count(False)

def get_shape_kong(CG, FL, MA_Line, FS, B1, B2):
    count = 0
    record = []
    compare_ma = []
    for cg, fl, ma, fs, b1, b2 in zip(CG, FL, MA_Line, FS, B1, B2):
        if math.isnan(cg) or math.isnan(fl) or math.isnan(fs):
            continue
        record.append(not cg == fl == fs)
        compare_ma.append(min(cg, fl, fs) < ma)
    pre = None
    for pos, b in enumerate(record):
        if pre is None:
            pre = b
            continue
        if b and pre == False:
            count += 1
            pre = b
            continue
        if not b and pos == len(record) - 1:
            count += 1
            pre = b
            continue
        pre = b
    return count, sum([1 for r, cp in zip(record, compare_ma) if not r and cp])

def order_callback(ContextInfo, orderInfo):
    print(orderInfo.m_strRemark, orderInfo.m_nOrderStatus, orderInfo.m_nOffsetFlag, orderInfo.m_dTradedPrice)
    if orderInfo.m_strRemark not in ['期权策略开多' + g.remark, '期权策略开空' + g.remark, 
                                   'K2信号变化开仓认购' + g.remark, 'K2信号变化开仓认沽' + g.remark,
                                   '到期前平仓认购' + g.remark, '到期前平仓认沽' + g.remark]:
        return
    marekt = {"SHFE": 'SF', "CZCE": 'ZF', "DCE": 'DF', "CFFEX": 'IF'}.get(orderInfo.m_strExchangeID, orderInfo.m_strExchangeID)
    k = orderInfo.m_strInstrumentID + '.' + marekt
    if k not in [g.call_one, g.put_one]:
        return
    if orderInfo.m_nOrderStatus == 56 and (orderInfo.m_strRemark.startswith('期权策略开多' + g.remark) or 
                                          orderInfo.m_strRemark.startswith('K2信号变化开仓认购' + g.remark)):
        g.buy_long += 1
        print(f"{g.code} 开多次数+1 {g.buy_long}")
    if orderInfo.m_nOrderStatus == 56 and (orderInfo.m_strRemark.startswith('期权策略开空' + g.remark) or 
                                          orderInfo.m_strRemark.startswith('K2信号变化开仓认沽' + g.remark)):
        g.buy_short += 1
        print(f"{g.code} 开空次数+1 {g.buy_short}")

    if orderInfo.m_nOrderStatus == 57 and orderInfo.m_nOffsetFlag == 48:
        g.hold = 0
        print("order_callback set 0")

def orderError_callback(ContextInfo, passOrderInfo, msg):
    if '期权策略' + g.remark in passOrderInfo.strategyName:
        g.hold = 0
        print("orderError_callback set 0", msg)
    if '期权策略开空' + g.remark in passOrderInfo.strategyName:
        g.buy_short += 1
        print(f"{g.code} 开空次数+1 {g.buy_short}")
    if '期权策略开多' + g.remark in passOrderInfo.strategyName:
        g.buy_long += 1
        print(f"{g.code} 开多次数+1 {g.buy_long}")

def deal_callback(ContextInfo, dealInfo):
    print(f"deal callback m_nOffsetFlag:[{dealInfo.m_nOffsetFlag}] m_strRemark:[{dealInfo.m_strRemark}], [{dealInfo.m_strInstrumentID}] [{dealInfo.m_strExchangeID}]  [{dealInfo.m_dPrice}]")
    if dealInfo.m_strRemark not in ['期权策略开多' + g.remark, '期权策略开空' + g.remark,
                                   'K2信号变化开仓认购' + g.remark, 'K2信号变化开仓认沽' + g.remark,
                                   '到期前平仓认购' + g.remark, '到期前平仓认沽' + g.remark]:
        return
    
    marekt = {"SHFE": 'SF', "CZCE": 'ZF', "DCE": 'DF', "CFFEX": 'IF'}.get(dealInfo.m_strExchangeID, dealInfo.m_strExchangeID)
    k = dealInfo.m_strInstrumentID + '.' + marekt
    if k not in [g.call_one, g.put_one]:
        return
    if dealInfo.m_nOffsetFlag == 48:
        print("deal callback", dealInfo.m_dPrice)
        g.open_price = round(dealInfo.m_dPrice, 4)
        g.hold_price = round(dealInfo.m_dPrice, 4)

def REF(S, N=1):  # 对序列整体下移动N,返回序列(shift后会产生NAN)    
    return pd.Series(S).shift(N).values  

def SMA(S, N, M=1):  # 中国式的SMA,至少需要120周期才精确 (雪球180周期)    
    return pd.Series(S).ewm(alpha=M/N, adjust=False).mean().values  

def SUM(S, N):  # 对序列求N天累计和，返回序列    
    return pd.Series(S).rolling(N).sum().values if N > 0 else pd.Series(S).cumsum().values  

def HHV(S, N):  # HHV(C, 5) 最近5天收盘最高价        
    return pd.Series(S).rolling(N).max().values     

def LLV(S, N):  # LLV(C, 5) 最近5天收盘最低价     
    return pd.Series(S).rolling(N).min().values    

def HHVBARS(S, N):  # 求N周期内S最高值到当前周期数, 返回序列
    return pd.Series(S).rolling(N).apply(lambda x: np.argmax(x[::-1]), raw=True).values 

def LLVBARS(S, N):  # 求N周期内S最低值到当前周期数, 返回序列
    return pd.Series(S).rolling(N).apply(lambda x: np.argmin(x[::-1]), raw=True).values    

def MA(S, N):  # 求序列的N日简单移动平均值，返回序列                    
    return pd.Series(S).rolling(N).mean().values  

def EMA(S, N):  # 指数移动平均,为了精度 S>4*N  EMA至少需要120周期     
    return pd.Series(S).ewm(span=N, adjust=False).mean().values     

def EMA(source, N: int, result_type='np'):
    """
    求指数平滑移动平均.
    用法:
    EMA(X,N),求X的N日指数平滑移动平均.算法：若Y=EMA(X,N)
    则Y=[2*X+(N-1)*Y']/(N+1),其中Y'表示上一周期Y值.
    例如：EMA(CLOSE,30)表示求30日指数平滑均价
    """
    M = 2
    if N < M:
        raise ValueError(f"N:{N}必须大于{M}")
    result = []
    temp = None
    d = N - 1
    for pos, x in enumerate(source):
        if pos == 0:
            result.append(x)
            temp = x
        else:
            temp = (M * x + d * temp) / (N + 1)
            result.append(temp)
    if result_type == 'np':
        return np.array(result)
    else:
        return result

def PyLLV(S, N):
    index = S.index
    result_list = []
    slist = list(S)

    for i in range(len(S.index)):
        l = slist[max(0, i + 1 - 34):i + 1]
        result_list.append(min(l))
    return pd.Series(result_list, index=index)

def PyHHV(S, N):
    index = S.index
    result_list = []
    slist = list(S)

    for i in range(len(S.index)):
        l = slist[max(0, i + 1 - 34):i + 1]
        result_list.append(max(l))
    return pd.Series(result_list, index=index)

def get_all_available_options(ContextInfo, undl_code, option_type):
    """
    获取所有可用的期权合约（所有月份）

    参数:
    ContextInfo: 上下文信息
    undl_code: 标的代码
    option_type: 期权类型，'CALL'或'PUT'

    返回:
    list: 期权合约代码列表
    """
    try:
        # 解析标的代码
        marketcodeList = undl_code.split('.')
        if len(marketcodeList) != 2:
            print(f"错误的标的代码格式: {undl_code}")
            return []

        undlCode = marketcodeList[0]
        undlMarket = marketcodeList[1]

        # 确定市场
        market = ""
        if undlMarket == "SH":
            if undlCode in ["000016", "000300", "000852", "000905"]:
                market = 'IF'
            else:
                market = "SHO"
        elif undlMarket == "SZ":
            market = "SZO"

        # 获取期权列表
        optList = []
        try:
            if market == 'SHO':
                optList += get_stock_list_in_sector('上证期权')
            elif market == 'SZO':
                optList += get_stock_list_in_sector('深证期权')
            elif market == 'IF':
                optList += get_stock_list_in_sector('中金所')
        except Exception as e:
            print(f"获取期权列表出错: {e}")
            return []

        # 过滤期权
        result = []
        now = time.strftime("%Y%m%d")

        for opt in optList:
            try:
                if opt.find(market) < 0:
                    continue

                inst = ContextInfo.get_option_detail_data(opt)
                if 'optType' not in inst:
                    continue

                # 检查期权类型
                if option_type.upper() != inst["optType"]:
                    continue

                # 检查到期日
                endDate = inst['EndDelivDate']
                if str(endDate) <= now:
                    continue

                # 检查开始交易日期
                createDate = inst.get('OpenDate', 0)
                openDate = inst.get('OpenDate', 0)
                if createDate >= 1:
                    openDate = min(openDate, createDate)
                if openDate < 20150101 or str(openDate) > now:
                    continue

                # 检查标的代码匹配
                if inst['ProductID'].find(undlCode) > 0 or inst.get('OptUndlCode', '') == undlCode:
                    result.append(opt)

            except Exception as e:
                print(f"处理期权合约 {opt} 时出错: {e}")
                continue

        print(f"找到 {len(result)} 个 {option_type} 期权合约")
        return result

    except Exception as e:
        print(f"获取期权合约列表时出错: {e}")
        return []

def get_option_real_one(ContextInfo):
    call_one = put_one = None
    now = time.strftime("%Y%m%d")
    expiry_days_threshold = 10  # 与全局参数保持一致

    # 获取所有可用认购合约（所有月份）
    call_list = get_all_available_options(ContextInfo, g.undl_code, 'CALL')
    undl_price = ContextInfo.get_full_tick([g.undl_code])[g.undl_code]['lastPrice']

    # 按到期月份分组
    call_by_month = {}
    for call in call_list:
        detail = ContextInfo.get_option_detail_data(call)
        end_date = str(detail['EndDelivDate'])
        month = end_date[:6]
        if month not in call_by_month:
            call_by_month[month] = []
        call_by_month[month].append(call)

    months = sorted(call_by_month.keys())
    use_month = None
    for i, month in enumerate(months):
        detail = ContextInfo.get_option_detail_data(call_by_month[month][0])
        end_date = str(detail['EndDelivDate'])
        days_to_expiry = (datetime.strptime(end_date, '%Y%m%d') - datetime.now()).days
        if days_to_expiry > expiry_days_threshold or i == len(months) - 1:
            use_month = month
            break

    if use_month:
        call_candidates = call_by_month[use_month]
        call_dict = {call: ContextInfo.get_option_detail_data(call)['OptExercisePrice'] for call in call_candidates}
        real_list = [code for code in call_dict if call_dict[code] < undl_price]
        real_list = sorted(real_list, key=lambda code: call_dict[code], reverse=True)
        if real_list:
            call_one = real_list[0]

    # 认沽同理
    put_list = get_all_available_options(ContextInfo, g.undl_code, 'PUT')
    put_by_month = {}
    for put in put_list:
        detail = ContextInfo.get_option_detail_data(put)
        end_date = str(detail['EndDelivDate'])
        month = end_date[:6]
        if month not in put_by_month:
            put_by_month[month] = []
        put_by_month[month].append(put)

    months = sorted(put_by_month.keys())
    use_month = None
    for i, month in enumerate(months):
        detail = ContextInfo.get_option_detail_data(put_by_month[month][0])
        end_date = str(detail['EndDelivDate'])
        days_to_expiry = (datetime.strptime(end_date, '%Y%m%d') - datetime.now()).days
        if days_to_expiry > expiry_days_threshold or i == len(months) - 1:
            use_month = month
            break

    if use_month:
        put_candidates = put_by_month[use_month]
        put_dict = {put: ContextInfo.get_option_detail_data(put)['OptExercisePrice'] for put in put_candidates}
        real_list = [code for code in put_dict if put_dict[code] > undl_price]
        real_list = sorted(real_list, key=lambda code: put_dict[code], reverse=False)
        if real_list:
            put_one = real_list[0]

    return call_one, put_one

def get_option_smart_selection(ContextInfo, direction, price_data):
    """
    智能期权合约选择（优化版）
    direction: 1为看涨(call), -1为看跌(put)
    """
    now = time.strftime("%Y%m%d")

    # 获取标的价格
    try:
        undl_price = ContextInfo.get_full_tick([g.undl_code])[g.undl_code]['lastPrice']
    except:
        undl_price = ContextInfo.get_market_data_ex(['close'], [g.undl_code], period='1m', count=1)[g.undl_code]['close'][0]

    # 计算当前市场波动率
    volatility = calculate_realized_volatility(price_data, volatility_lookback_days)

    # 根据波动率和方向选择最优行权价
    def get_optimal_strike(undl_price, volatility, direction):
        if volatility > high_volatility_threshold:  # 高波动选ATM
            return undl_price
        elif volatility < min_volatility_threshold * 1.5:  # 低波动选轻度ITM
            offset = undl_price * 0.02  # 2%偏移
            return undl_price - offset if direction > 0 else undl_price + offset
        else:  # 中等波动选轻度OTM
            offset = undl_price * 0.015  # 1.5%偏移
            return undl_price + offset if direction > 0 else undl_price - offset

    optimal_strike = get_optimal_strike(undl_price, volatility, direction)
    print(f"智能合约选择: 标的价格={undl_price:.2f}, 波动率={volatility:.3f}, 目标行权价={optimal_strike:.2f}")

    if direction > 0:  # 选择认购期权
        option_list = get_all_available_options(ContextInfo, g.undl_code, 'CALL')
    else:  # 选择认沽期权
        option_list = get_all_available_options(ContextInfo, g.undl_code, 'PUT')

    # 按到期月份分组
    option_by_month = {}
    for option in option_list:
        try:
            detail = ContextInfo.get_option_detail_data(option)
            end_date = str(detail['EndDelivDate'])
            month = end_date[:6]
            if month not in option_by_month:
                option_by_month[month] = []
            option_by_month[month].append(option)
        except:
            continue

    # 选择合适的到期月份
    months = sorted(option_by_month.keys())
    use_month = None
    for i, month in enumerate(months):
        try:
            detail = ContextInfo.get_option_detail_data(option_by_month[month][0])
            end_date = str(detail['EndDelivDate'])
            days_to_expiry = (datetime.strptime(end_date, '%Y%m%d') - datetime.now()).days
            if days_to_expiry > expiry_days_threshold or i == len(months) - 1:
                use_month = month
                break
        except:
            continue

    if not use_month:
        print("未找到合适的期权合约月份")
        return None

    # 在选定月份中找到最接近目标行权价的合约
    option_candidates = option_by_month[use_month]
    best_option = None
    min_diff = float('inf')

    for option in option_candidates:
        try:
            strike_price = ContextInfo.get_option_detail_data(option)['OptExercisePrice']
            diff = abs(strike_price - optimal_strike)

            # 流动性检查
            if enable_liquidity_check and not check_option_liquidity(ContextInfo, option):
                continue

            if diff < min_diff:
                min_diff = diff
                best_option = option

        except Exception as e:
            print(f"处理期权合约 {option} 时出错: {e}")
            continue

    if best_option:
        try:
            strike_price = ContextInfo.get_option_detail_data(best_option)['OptExercisePrice']
            option_type = "认购" if direction > 0 else "认沽"
            print(f"选中{option_type}期权: {best_option}, 行权价: {strike_price}, 目标行权价: {optimal_strike:.2f}")
        except:
            pass
    else:
        print(f"未找到合适的期权合约")

    return best_option

def get_current_month_option(ContextInfo, object, dedate, opttype=""):
    # dedate 日期 %Y%m%d 
    # 获取截止到ddate这天还未到行权日的当月期权合约
    isavailavle = True
    result = []
    opt_by_month = {}
    undlMarket = ""
    undlCode = ""
    marketcodeList = object.split('.')
    if len(marketcodeList) != 2:
        return []
    undlCode = marketcodeList[0]
    undlMarket = marketcodeList[1]
    market = ""
    if undlMarket == "SH":
        if undlCode in ["000016", "000300", "000852", "000905"]:
            market = 'IF'
        else:
            market = "SHO"
    elif undlMarket == "SZ":
        market = "SZO"
    if opttype.upper() == "C":
        opttype = "CALL"
    elif opttype.upper() == "P":
        opttype = "PUT"
    optList = []
    if market == 'SHO':
        optList += get_stock_list_in_sector('上证期权')
    elif market == 'SZO':
        optList += get_stock_list_in_sector('深证期权')
    elif market == 'IF':
        optList += get_stock_list_in_sector('中金所')
    for opt in optList:
        if opt.find(market) < 0:
            continue
        inst = ContextInfo.get_option_detail_data(opt)
        if 'optType' not in inst:
            continue
        endDate = inst['EndDelivDate']
        if isavailavle and str(endDate) <= dedate:
            continue
        if opttype.upper() != "" and opttype.upper() != inst["optType"]:
            continue
        if 1:  # option is trade, guosen demand
            createDate = inst['OpenDate']
            openDate = inst['OpenDate']
            if createDate >= 1:
                openDate = min(openDate, createDate)
            if openDate < 20150101 or str(openDate) > dedate:
                continue
        if inst['ProductID'].find(undlCode) > 0 or inst['OptUndlCode'] == undlCode:
            result.append(opt)
            month = str(endDate)[:6]
            if month not in opt_by_month:
                opt_by_month[month] = [opt]
            else:
                opt_by_month[month].append(opt)
    opt_list = sorted(opt_by_month, reverse=False)
    print(opt_by_month.keys())
    for opt in opt_by_month:
        if opt_list:
            b = datetime.strptime(opt_list[0], '%Y%m')
            a = datetime.now()
            if (b - a).days < 15:
                return opt_by_month[opt_list[0]]
            else:
                print(object, '未找到期权', opt_list[0], opt_by_month)
                return []
    else:
        return []

def calculate_lines(price_data):
    """计算红色横线值"""
    if price_data.empty:
        return 0, 0
        
    # 打印用于计算的价格数据
    print(f"用于计算红色横线的价格数据: {price_data['high']}")
    
    # 修改计算逻辑，使用移动平均或其他方法
    if len(price_data) >= 2:
        # 使用最近两根K线的最高价的平均值
        RED_LINE = price_data['high'].tail(2).mean()
    else:
        RED_LINE = price_data['high'].iloc[-1]
    
    print(f"计算过程: 使用最近{len(price_data)}根K线计算得到红色横线值: {RED_LINE:.4f}")
    
    return RED_LINE, None

